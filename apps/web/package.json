{"name": "web", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "eslint . --max-warnings 0", "start": "next start"}, "dependencies": {"@repo/ui": "workspace:*", "@tailwindcss/postcss": "^4.1.11", "auth": "workspace:*", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.1", "lucide-react": "^0.525.0", "motion": "^12.23.7", "next": "^14.1.1", "next-auth": "^4.24.11", "octokit": "^5.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.2.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@next/eslint-plugin-next": "^14.1.1", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "5.5.4"}}