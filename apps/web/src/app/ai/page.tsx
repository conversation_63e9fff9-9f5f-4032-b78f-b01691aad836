"use client";

import Sidebar from "@/components/sidebar";
import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../packages/store/store";

const AI = () => {
  const { commit } = useSelector((state: RootState) => state.repo);

  return (
    <div className="min-h-screen bg-slate-900 text-white font-sans">
      <Sidebar />
      <div className="ml-64 p-6">
        <h1 className="text-3xl font-bold text-white mb-6">AI Q&A</h1>

        <div className="bg-slate-800 p-4 rounded-lg">
          <p className="text-lg">
            {commit.length > 0 ? (
              <span className="text-green-400">
                ✅ {commit.length} commits loaded from store
              </span>
            ) : (
              <span className="text-gray-400">❌ No commit data available</span>
            )}
          </p>
          <p className="text-sm text-gray-400 mt-2">
            This data persists across route changes and page refreshes!
          </p>
        </div>
      </div>
    </div>
  );
};

export default AI;
