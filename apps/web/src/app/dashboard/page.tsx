"use client";

import Sidebar from "@/components/sidebar";
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../packages/store/store";

export default function Dashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();

  // Access store data
  const { repoData, folderstructure, commit } = useSelector(
    (state: RootState) => state.repo
  );

  useEffect(() => {
    if (status === "loading") return;
    if (!session) router.push("/auth/signin");
  }, [session, status, router]);

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400"></div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white font-sans">
      <Sidebar />
      <div className="ml-64 p-6">
        <h1 className="text-3xl font-bold text-white mb-6">Dashboard</h1>

        {/* Display store data to verify persistence */}
        <div className="space-y-6">
          <div className="bg-slate-800 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-3">Repository Data</h2>
            {repoData.length > 0 ? (
              <div className="space-y-2">
                {repoData.map((repo, index) => (
                  <div key={index} className="text-sm">
                    <p>
                      <span className="text-blue-400">Name:</span> {repo.name}
                    </p>
                    <p>
                      <span className="text-blue-400">Description:</span>{" "}
                      {repo.description || "No description"}
                    </p>
                    <p>
                      <span className="text-blue-400">Languages:</span>{" "}
                      {repo.language && Object.keys(repo.language).length > 0
                        ? Object.keys(repo.language).join(", ")
                        : "Not specified"}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-400">
                No repository data available. Please analyze a repository first.
              </p>
            )}
          </div>

          <div className="bg-slate-800 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-3">Folder Structure</h2>
            {folderstructure.length > 0 ? (
              <p className="text-green-400">
                {folderstructure.length} items in folder structure
              </p>
            ) : (
              <p className="text-gray-400">
                No folder structure data available.
              </p>
            )}
          </div>

          <div className="bg-slate-800 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-3">Commits</h2>
            {commit.length > 0 ? (
              <p className="text-green-400">{commit.length} commits loaded</p>
            ) : (
              <p className="text-gray-400">No commit data available.</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
