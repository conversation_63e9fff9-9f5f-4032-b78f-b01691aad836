"use client";

import Sidebar from "@/components/sidebar";
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../packages/store/store";

export default function Dashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();

  // Access store data
  const { repoData, folderstructure, commit } = useSelector(
    (state: RootState) => state.repo
  );

  useEffect(() => {
    if (status === "loading") return;
    if (!session) router.push("/auth/signin");
  }, [session, status, router]);

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400"></div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white font-sans">
      <Sidebar />
      <div className="ml-64 p-6">
        <h1 className="text-3xl font-bold text-white mb-6">Dashboard</h1>

        {/* Display store data to verify persistence */}
        <div className="bg-slate-800 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-3">Store Status</h2>
          <div className="space-y-2 text-sm">
            <p>
              <span className="text-blue-400">Repository Data:</span>{" "}
              <span
                className={
                  repoData.length > 0 ? "text-green-400" : "text-gray-400"
                }
              >
                {repoData.length > 0
                  ? `${repoData.length} repo(s) loaded`
                  : "No data"}
              </span>
            </p>
            <p>
              <span className="text-blue-400">Folder Structure:</span>{" "}
              <span
                className={
                  folderstructure.length > 0
                    ? "text-green-400"
                    : "text-gray-400"
                }
              >
                {folderstructure.length > 0
                  ? `${folderstructure.length} items`
                  : "No data"}
              </span>
            </p>
            <p>
              <span className="text-blue-400">Commits:</span>{" "}
              <span
                className={
                  commit.length > 0 ? "text-green-400" : "text-gray-400"
                }
              >
                {commit.length > 0 ? `${commit.length} commits` : "No data"}
              </span>
            </p>
          </div>
          {repoData.length === 0 && (
            <p className="text-yellow-400 text-sm mt-3">
              💡 Go to the home page and analyze a repository to see data
              persistence in action!
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
