"use client";

import Sidebar from "@/components/sidebar";
import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../packages/store/store";

const FolderStructure = () => {
  const { folderstructure } = useSelector((state: RootState) => state.repo);

  return (
    <div className="min-h-screen bg-slate-900 text-white font-sans">
      <Sidebar />
      <div className="ml-64 p-6">
        <h1 className="text-3xl font-bold text-white mb-6">Folder Structure</h1>

        <div className="bg-slate-800 p-4 rounded-lg">
          {folderstructure.length > 0 ? (
            <div className="space-y-2">
              <p className="text-green-400 mb-4">
                {folderstructure.length} items found
              </p>
              <div className="max-h-96 overflow-y-auto">
                {folderstructure.slice(0, 50).map((item, index) => (
                  <div
                    key={index}
                    className="text-sm py-1 border-b border-slate-700 last:border-b-0"
                  >
                    <span
                      className={`inline-block w-12 text-xs ${
                        item.type === "tree"
                          ? "text-blue-400"
                          : "text-green-400"
                      }`}
                    >
                      {item.type === "tree" ? "📁" : "📄"}
                    </span>
                    <span className="text-white">{item.path}</span>
                    {item.type === "blob" && (
                      <span className="text-gray-400 ml-2">
                        ({item.size} bytes)
                      </span>
                    )}
                  </div>
                ))}
                {folderstructure.length > 50 && (
                  <p className="text-gray-400 text-sm mt-2">
                    ... and {folderstructure.length - 50} more items
                  </p>
                )}
              </div>
            </div>
          ) : (
            <p className="text-gray-400">
              No folder structure data available. Please analyze a repository
              first.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default FolderStructure;
