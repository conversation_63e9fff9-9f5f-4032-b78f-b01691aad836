"use client";

import Sidebar from "@/components/sidebar";
import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../packages/store/store";

const TechStack = () => {
  const { repoData } = useSelector((state: RootState) => state.repo);

  const getLanguageStats = () => {
    if (repoData.length === 0) return null;

    const languages = repoData[0]?.language || {};
    const total = Object.values(languages).reduce(
      (sum, bytes) => sum + bytes,
      0
    );

    return Object.entries(languages)
      .map(([lang, bytes]) => ({
        name: lang,
        bytes,
        percentage: ((bytes / total) * 100).toFixed(1),
      }))
      .sort((a, b) => b.bytes - a.bytes);
  };

  const languageStats = getLanguageStats();

  return (
    <div className="min-h-screen bg-slate-900 text-white font-sans">
      <Sidebar />
      <div className="ml-64 p-6">
        <h1 className="text-3xl font-bold text-white mb-6">Tech Stack</h1>

        <div className="bg-slate-800 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Programming Languages</h2>
          {languageStats ? (
            <div className="space-y-3">
              {languageStats.map((lang, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 rounded-full bg-gradient-to-r from-blue-500 to-purple-500"></div>
                    <span className="text-white font-medium">{lang.name}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-32 bg-slate-700 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                        style={{ width: `${lang.percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-gray-400 text-sm w-12 text-right">
                      {lang.percentage}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-400">
              No tech stack data available. Please analyze a repository first.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default TechStack;
