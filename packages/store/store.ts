import { configureStore } from "@reduxjs/toolkit";
import repoReducer from "../slice/repoSlice";

// Load state from localStorage
const loadState = () => {
  try {
    if (typeof window === 'undefined') return undefined;
    const serializedState = localStorage.getItem('gitgist-state');
    if (serializedState === null) return undefined;
    return JSON.parse(serializedState);
  } catch (err) {
    return undefined;
  }
};

// Save state to localStorage
const saveState = (state: any) => {
  try {
    if (typeof window === 'undefined') return;
    const serializedState = JSON.stringify(state);
    localStorage.setItem('gitgist-state', serializedState);
  } catch (err) {
    // Ignore write errors
  }
};

const persistedState = loadState();

export const store = configureStore({
  reducer: {
    repo: repoReducer,
  },
  preloadedState: persistedState as any,
});

// Subscribe to store changes and save to localStorage
store.subscribe(() => {
  saveState(store.getState());
});

export type AppStore = typeof store;
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
