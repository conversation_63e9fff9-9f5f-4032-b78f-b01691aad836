import { configureStore } from "@reduxjs/toolkit";
import { repoSlice } from "../slice/repoSlice";

export const store = configureStore({
  reducer: {
    repo: repoSlice.reducer,
  },
});

// Load persisted state after store creation
const loadPersistedState = () => {
  try {
    if (typeof window === 'undefined') return;
    const serializedState = localStorage.getItem('gitgist-state');
    if (serializedState === null) return;
    const persistedState = JSON.parse(serializedState);

    // Dispatch actions to restore state
    if (persistedState.repo) {
      if (persistedState.repo.repoData) {
        store.dispatch(repoSlice.actions.setRepoData(persistedState.repo.repoData));
      }
      if (persistedState.repo.folderstructure) {
        store.dispatch(repoSlice.actions.setFolderStructure(persistedState.repo.folderstructure));
      }
      if (persistedState.repo.commit) {
        store.dispatch(repoSlice.actions.setCommit(persistedState.repo.commit));
      }
    }
  } catch (err) {
    // Ignore errors
  }
};

// Save state to localStorage
const saveState = (state: any) => {
  try {
    if (typeof window === 'undefined') return;
    const serializedState = JSON.stringify(state);
    localStorage.setItem('gitgist-state', serializedState);
  } catch (err) {
    // Ignore write errors
  }
};

// Load persisted state on initialization
if (typeof window !== 'undefined') {
  loadPersistedState();
}

// Subscribe to store changes and save to localStorage
store.subscribe(() => {
  saveState(store.getState());
});

export type AppStore = typeof store;
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
